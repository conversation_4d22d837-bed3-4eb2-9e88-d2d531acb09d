import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { ENV } from './aws-clients';

const region = process.env.AWS_REGION || 'us-east-1';
const client = new DynamoDBClient({ region });
const docClient = DynamoDBDocumentClient.from(client);

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyPrefix: string; // Prefix for the rate limit key
}

interface RateLimitResult {
  allowed: boolean;
  remainingRequests: number;
  resetTime: number;
  retryAfter?: number;
}

/**
 * Rate limiter using DynamoDB for distributed rate limiting
 */
export class RateLimiter {
  private config: RateLimitConfig;
  private tableName: string;

  constructor(config: RateLimitConfig, tableName: string = ENV.TRANSFER_TABLE) {
    this.config = config;
    this.tableName = tableName;
  }

  /**
   * Check if a request is allowed based on rate limiting rules
   */
  async checkRateLimit(identifier: string): Promise<RateLimitResult> {
    const now = Date.now();
    const windowStart = Math.floor(now / this.config.windowMs) * this.config.windowMs;
    const key = `${this.config.keyPrefix}:${identifier}:${windowStart}`;

    try {
      // Try to get existing rate limit record
      const getResult = await docClient.send(new GetCommand({
        TableName: this.tableName,
        Key: { transferId: key }
      }));

      let currentCount = 0;
      let resetTime = windowStart + this.config.windowMs;

      if (getResult.Item) {
        currentCount = getResult.Item.requestCount || 0;
        resetTime = getResult.Item.resetTime || resetTime;
      }

      // Check if limit exceeded
      if (currentCount >= this.config.maxRequests) {
        const retryAfter = Math.ceil((resetTime - now) / 1000);
        return {
          allowed: false,
          remainingRequests: 0,
          resetTime,
          retryAfter: retryAfter > 0 ? retryAfter : 1
        };
      }

      // Increment counter
      const newCount = currentCount + 1;
      
      if (getResult.Item) {
        // Update existing record
        await docClient.send(new UpdateCommand({
          TableName: this.tableName,
          Key: { transferId: key },
          UpdateExpression: 'SET requestCount = :count',
          ExpressionAttributeValues: {
            ':count': newCount
          }
        }));
      } else {
        // Create new record
        await docClient.send(new PutCommand({
          TableName: this.tableName,
          Item: {
            transferId: key,
            requestCount: newCount,
            resetTime,
            createdAt: now,
            // Set TTL to automatically clean up old records
            ttl: Math.floor((resetTime + this.config.windowMs) / 1000)
          }
        }));
      }

      return {
        allowed: true,
        remainingRequests: this.config.maxRequests - newCount,
        resetTime
      };

    } catch (error) {
      console.error('Rate limiter error:', error);
      // On error, allow the request (fail open)
      return {
        allowed: true,
        remainingRequests: this.config.maxRequests - 1,
        resetTime: windowStart + this.config.windowMs
      };
    }
  }
}

// Pre-configured rate limiters for different endpoints
export const userCreationLimiter = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5, // 5 account creations per 15 minutes per IP
  keyPrefix: 'user_creation'
});

export const emailLimiter = new RateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  maxRequests: 10, // 10 emails per hour per email address
  keyPrefix: 'email_send'
});

export const uploadValidationLimiter = new RateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 30, // 30 validation requests per minute per IP
  keyPrefix: 'upload_validation'
});

/**
 * Extract client IP from API Gateway event
 */
export function getClientIP(event: any): string {
  // Try various headers for client IP
  const headers = event.headers || {};
  const multiValueHeaders = event.multiValueHeaders || {};
  
  // Check common headers for real IP
  const ipHeaders = [
    'x-forwarded-for',
    'x-real-ip',
    'x-client-ip',
    'cf-connecting-ip', // Cloudflare
    'true-client-ip'
  ];

  for (const header of ipHeaders) {
    const value = headers[header] || headers[header.toLowerCase()];
    if (value) {
      // x-forwarded-for can contain multiple IPs, take the first one
      return value.split(',')[0].trim();
    }
  }

  // Fallback to source IP
  return event.requestContext?.identity?.sourceIp || 'unknown';
}

/**
 * Input sanitization utilities
 */
export const sanitize = {
  email: (email: string): string => {
    return email.trim().toLowerCase();
  },

  name: (name: string): string => {
    // Remove potentially dangerous characters but keep international characters
    return name.trim().replace(/[<>\"'&]/g, '');
  },

  password: (password: string): string => {
    // Don't modify password, just validate length
    return password;
  },

  transferId: (id: string): string => {
    // Only allow alphanumeric characters and hyphens
    return id.replace(/[^a-zA-Z0-9-]/g, '');
  }
};

/**
 * Validation utilities
 */
export const validate = {
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  },

  name: (name: string): boolean => {
    return name.length >= 1 && name.length <= 100;
  },

  password: (password: string): boolean => {
    // Optional password, but if provided must be at least 8 characters
    return password.length === 0 || password.length >= 8;
  },

  fileSize: (size: number): boolean => {
    return size > 0 && size <= 50 * 1024 * 1024 * 1024; // 50GB max
  }
};
