import * as path from 'path';
import * as fs from 'fs-extra';
import { spawn, exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface CompressionResult {
  compressedPath: string;
  compressionTime: number;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  method: string;
}

export interface DecompressionResult {
  extractedFiles: string[];
  decompressionTime: number;
  method: string;
}

export enum CompressionMethod {
  BASIC_ZMT = 'basic_zmt',
  ADVANCED_ZMT = 'advanced_zmt_m5',
  VIDEO_MP4 = 'video_mp4',
  AUDIO = 'audio',
  VIDEO_Y4M = 'video_y4m',
  TIFF_IMAGE = 'tiff_image',
  DICOM = 'dicom'
}

export class CompressionRouter {
  private readonly scriptsDir: string;
  private readonly zmtBinary: string;
  private readonly zmtDcmBinary: string;

  constructor(scriptsDir: string = '/opt/fasttransfer/scripts') {
    this.scriptsDir = scriptsDir;
    this.zmtBinary = path.join(scriptsDir, 'zmt');
    this.zmtDcmBinary = path.join(scriptsDir, 'zmtdcm');
  }

  /**
   * Determine the appropriate compression method based on file extension
   */
  public getCompressionMethod(fileName: string, useAdvanced: boolean = false): CompressionMethod {
    const ext = path.extname(fileName).toLowerCase();
    
    // Basic ZMT compression files
    const basicZmtExtensions = ['.txt', '.pdf', '.xls', '.xlsx', '.doc', '.docx', '.psd', '.csv', '.db', '.ppt', '.pptx'];
    if (basicZmtExtensions.includes(ext)) {
      return useAdvanced ? CompressionMethod.ADVANCED_ZMT : CompressionMethod.BASIC_ZMT;
    }

    // Video files for MP4 compression
    const videoMp4Extensions = ['.mp4', '.mkv', '.3gp', '.avi', '.mov', '.webm', '.flv'];
    if (videoMp4Extensions.includes(ext)) {
      return CompressionMethod.VIDEO_MP4;
    }

    // Audio files
    const audioExtensions = ['.mp3', '.aac', '.opus', '.m4a'];
    if (audioExtensions.includes(ext)) {
      return CompressionMethod.AUDIO;
    }

    // Y4M video files
    if (ext === '.y4m') {
      return CompressionMethod.VIDEO_Y4M;
    }

    // TIFF images
    if (ext === '.tiff' || ext === '.tif') {
      return CompressionMethod.TIFF_IMAGE;
    }

    // DICOM files
    if (ext === '.dcm') {
      return CompressionMethod.DICOM;
    }

    // Default to basic ZMT for unknown file types
    return useAdvanced ? CompressionMethod.ADVANCED_ZMT : CompressionMethod.BASIC_ZMT;
  }

  /**
   * Compress files using the appropriate method
   */
  public async compressFiles(filePaths: string[], outputPath: string, method?: CompressionMethod): Promise<CompressionResult> {
    const startTime = Date.now();
    
    // If method not specified, determine from first file
    if (!method && filePaths.length > 0) {
      method = this.getCompressionMethod(filePaths[0]);
    }
    
    if (!method) {
      throw new Error('Unable to determine compression method');
    }

    // Calculate original size
    let originalSize = 0;
    for (const filePath of filePaths) {
      const stats = await fs.stat(filePath);
      originalSize += stats.size;
    }

    let compressedPath: string;
    
    switch (method) {
      case CompressionMethod.BASIC_ZMT:
        compressedPath = await this.compressWithBasicZMT(filePaths, outputPath);
        break;
      case CompressionMethod.ADVANCED_ZMT:
        compressedPath = await this.compressWithAdvancedZMT(filePaths, outputPath);
        break;
      case CompressionMethod.VIDEO_MP4:
        compressedPath = await this.compressWithVideoMp4(filePaths, outputPath);
        break;
      case CompressionMethod.AUDIO:
        compressedPath = await this.compressWithAudio(filePaths, outputPath);
        break;
      case CompressionMethod.VIDEO_Y4M:
        compressedPath = await this.compressWithVideoY4M(filePaths, outputPath);
        break;
      case CompressionMethod.TIFF_IMAGE:
        compressedPath = await this.compressWithTiffImage(filePaths, outputPath);
        break;
      case CompressionMethod.DICOM:
        compressedPath = await this.compressWithDicom(filePaths, outputPath);
        break;
      default:
        throw new Error(`Unsupported compression method: ${method}`);
    }

    const compressionTime = Date.now() - startTime;
    
    // Get compressed file size
    const compressedStats = await fs.stat(compressedPath);
    const compressedSize = compressedStats.size;
    const compressionRatio = Math.round(((originalSize - compressedSize) / originalSize) * 100);

    return {
      compressedPath,
      compressionTime,
      originalSize,
      compressedSize,
      compressionRatio,
      method: method.toString()
    };
  }

  /**
   * Extract files using the appropriate method
   */
  public async extractFiles(compressedPath: string, outputDir: string, method: CompressionMethod): Promise<DecompressionResult> {
    const startTime = Date.now();
    
    await fs.ensureDir(outputDir);
    
    let extractedFiles: string[];
    
    switch (method) {
      case CompressionMethod.BASIC_ZMT:
      case CompressionMethod.ADVANCED_ZMT:
        extractedFiles = await this.extractWithZMT(compressedPath, outputDir);
        break;
      case CompressionMethod.VIDEO_MP4:
      case CompressionMethod.AUDIO:
      case CompressionMethod.VIDEO_Y4M:
        // These methods create new files, so we need to handle extraction differently
        extractedFiles = await this.extractMediaFiles(compressedPath, outputDir, method);
        break;
      case CompressionMethod.TIFF_IMAGE:
        extractedFiles = await this.extractTiffImage(compressedPath, outputDir);
        break;
      case CompressionMethod.DICOM:
        extractedFiles = await this.extractWithDicom(compressedPath, outputDir);
        break;
      default:
        throw new Error(`Unsupported extraction method: ${method}`);
    }

    const decompressionTime = Date.now() - startTime;

    return {
      extractedFiles,
      decompressionTime,
      method: method.toString()
    };
  }

  /**
   * Basic ZMT compression
   */
  private async compressWithBasicZMT(filePaths: string[], outputPath: string): Promise<string> {
    const workDir = path.dirname(outputPath);
    const outputFileName = path.basename(outputPath);
    
    // Create a list of files to compress
    const fileNames = filePaths.map(fp => path.basename(fp));
    const command = `cd "${workDir}" && "${this.zmtBinary}" a "${outputFileName}" ${fileNames.map(f => `"${f}"`).join(' ')}`;
    
    console.log(`🗜️ Running basic ZMT compression: ${command}`);
    const { stdout, stderr } = await execAsync(command);
    
    if (stderr) {
      console.log('ZMT compression stderr:', stderr);
    }
    
    if (!await fs.pathExists(outputPath)) {
      throw new Error('Basic ZMT compression failed - output file not created');
    }
    
    return outputPath;
  }

  /**
   * Advanced ZMT compression with -m5 mode
   */
  private async compressWithAdvancedZMT(filePaths: string[], outputPath: string): Promise<string> {
    const workDir = path.dirname(outputPath);
    const outputFileName = path.basename(outputPath);
    
    // Create a list of files to compress
    const fileNames = filePaths.map(fp => path.basename(fp));
    const command = `cd "${workDir}" && "${this.zmtBinary}" a -m5 "${outputFileName}" ${fileNames.map(f => `"${f}"`).join(' ')}`;
    
    console.log(`🗜️ Running advanced ZMT compression: ${command}`);
    const { stdout, stderr } = await execAsync(command);
    
    if (stderr) {
      console.log('Advanced ZMT compression stderr:', stderr);
    }
    
    if (!await fs.pathExists(outputPath)) {
      throw new Error('Advanced ZMT compression failed - output file not created');
    }
    
    return outputPath;
  }

  /**
   * Video MP4 compression using FFmpeg
   */
  private async compressWithVideoMp4(filePaths: string[], outputPath: string): Promise<string> {
    if (filePaths.length !== 1) {
      throw new Error('Video compression only supports single file input');
    }
    
    const inputPath = filePaths[0];
    const workDir = path.dirname(outputPath);
    
    // Create encoded directory structure
    const encodedDir = path.join(workDir, 'encoded');
    const originalDir = path.join(workDir, 'original');
    await fs.ensureDir(encodedDir);
    await fs.ensureDir(originalDir);
    
    const inputFileName = path.basename(inputPath);
    const outputFileName = `${path.parse(inputFileName).name}_zmt.mp4`;
    const finalOutputPath = path.join(encodedDir, outputFileName);
    
    // Copy input file to work directory for processing
    const workInputPath = path.join(workDir, inputFileName);
    await fs.copy(inputPath, workInputPath);
    
    // Run the video compression script
    const scriptPath = path.join(this.scriptsDir, 'compress_code_mp4_update.sh');
    const ext = path.extname(inputFileName).substring(1); // Remove the dot
    const command = `cd "${workDir}" && bash "${scriptPath}" "${ext}"`;
    
    console.log(`🎥 Running video compression: ${command}`);
    const { stdout, stderr } = await execAsync(command);
    
    if (stderr) {
      console.log('Video compression stderr:', stderr);
    }
    
    if (!await fs.pathExists(finalOutputPath)) {
      throw new Error('Video compression failed - output file not created');
    }
    
    // Copy the compressed file to the final output location
    await fs.copy(finalOutputPath, outputPath);
    
    return outputPath;
  }

  /**
   * Audio compression
   */
  private async compressWithAudio(filePaths: string[], outputPath: string): Promise<string> {
    if (filePaths.length !== 1) {
      throw new Error('Audio compression only supports single file input');
    }

    const inputPath = filePaths[0];
    const workDir = path.dirname(outputPath);

    // Create encoded directory structure
    const encodedDir = path.join(workDir, 'encoded');
    const originalDir = path.join(workDir, 'original');
    await fs.ensureDir(encodedDir);
    await fs.ensureDir(originalDir);

    const inputFileName = path.basename(inputPath);
    const outputFileName = `${path.parse(inputFileName).name}_zmt.mp3`;
    const finalOutputPath = path.join(encodedDir, outputFileName);

    // Copy input file to work directory for processing
    const workInputPath = path.join(workDir, inputFileName);
    await fs.copy(inputPath, workInputPath);

    // Run audio compression using FFmpeg directly (since compress_audio.sh doesn't exist)
    const command = `cd "${workDir}" && ffmpeg -y -hide_banner -loglevel error -i "${inputFileName}" -codec:a libmp3lame -b:a 128k "${path.join('encoded', outputFileName)}" && mv "${inputFileName}" original/`;

    console.log(`🎵 Running audio compression: ${command}`);
    const { stdout, stderr } = await execAsync(command);

    if (stderr) {
      console.log('Audio compression stderr:', stderr);
    }

    if (!await fs.pathExists(finalOutputPath)) {
      throw new Error('Audio compression failed - output file not created');
    }

    // Copy the compressed file to the final output location
    await fs.copy(finalOutputPath, outputPath);

    return outputPath;
  }

  /**
   * Y4M video compression
   */
  private async compressWithVideoY4M(filePaths: string[], outputPath: string): Promise<string> {
    if (filePaths.length !== 1) {
      throw new Error('Y4M video compression only supports single file input');
    }

    const inputPath = filePaths[0];
    const workDir = path.dirname(outputPath);

    // Create encoded directory structure
    const encodedDir = path.join(workDir, 'encoded');
    const originalDir = path.join(workDir, 'original');
    await fs.ensureDir(encodedDir);
    await fs.ensureDir(originalDir);

    const inputFileName = path.basename(inputPath);
    const outputFileName = `${path.parse(inputFileName).name}_zmt.mp4`;
    const finalOutputPath = path.join(encodedDir, outputFileName);

    // Copy input file to work directory for processing
    const workInputPath = path.join(workDir, inputFileName);
    await fs.copy(inputPath, workInputPath);

    // Run the Y4M video compression script
    const scriptPath = path.join(this.scriptsDir, 'compress_code_video.sh');
    const command = `cd "${workDir}" && bash "${scriptPath}"`;

    console.log(`🎬 Running Y4M video compression: ${command}`);
    const { stdout, stderr } = await execAsync(command);

    if (stderr) {
      console.log('Y4M video compression stderr:', stderr);
    }

    if (!await fs.pathExists(finalOutputPath)) {
      throw new Error('Y4M video compression failed - output file not created');
    }

    // Copy the compressed file to the final output location
    await fs.copy(finalOutputPath, outputPath);

    return outputPath;
  }

  /**
   * TIFF image compression using Python script
   */
  private async compressWithTiffImage(filePaths: string[], outputPath: string): Promise<string> {
    if (filePaths.length !== 1) {
      throw new Error('TIFF image compression only supports single file input');
    }

    const inputPath = filePaths[0];
    const workDir = path.dirname(outputPath);
    const inputFileName = path.basename(inputPath);

    // Copy input file to work directory for processing
    const workInputPath = path.join(workDir, inputFileName);
    await fs.copy(inputPath, workInputPath);

    // Run the TIFF compression Python script
    const scriptPath = path.join(this.scriptsDir, 'zmt_image.py');
    const command = `cd "${workDir}" && python3 "${scriptPath}" "${inputFileName}"`;

    console.log(`🖼️ Running TIFF image compression: ${command}`);
    const { stdout, stderr } = await execAsync(command);

    if (stderr) {
      console.log('TIFF compression stderr:', stderr);
    }

    // The Python script creates multiple output files, we'll use the best compression result
    // Look for the ZMT compressed files
    const zmtFiles = await fs.readdir(workDir);
    const zmtFile = zmtFiles.find(f => f.includes('_zmt_') && f.endsWith('.tif'));

    if (!zmtFile) {
      throw new Error('TIFF compression failed - no ZMT output file found');
    }

    const zmtFilePath = path.join(workDir, zmtFile);
    await fs.copy(zmtFilePath, outputPath);

    return outputPath;
  }

  /**
   * DICOM compression using binary
   */
  private async compressWithDicom(filePaths: string[], outputPath: string): Promise<string> {
    const workDir = path.dirname(outputPath);
    const outputFileName = path.basename(outputPath);

    // Create a list of files to compress
    const fileNames = filePaths.map(fp => path.basename(fp));
    const command = `cd "${workDir}" && "${this.zmtDcmBinary}" a "${outputFileName}" ${fileNames.map(f => `"${f}"`).join(' ')}`;

    console.log(`🏥 Running DICOM compression: ${command}`);
    const { stdout, stderr } = await execAsync(command);

    if (stderr) {
      console.log('DICOM compression stderr:', stderr);
    }

    if (!await fs.pathExists(outputPath)) {
      throw new Error('DICOM compression failed - output file not created');
    }

    return outputPath;
  }

  /**
   * Extract files using ZMT binary
   */
  private async extractWithZMT(compressedPath: string, outputDir: string): Promise<string[]> {
    const command = `cd "${outputDir}" && "${this.zmtBinary}" x "${compressedPath}"`;

    console.log(`📂 Running ZMT extraction: ${command}`);
    const { stdout, stderr } = await execAsync(command);

    if (stderr) {
      console.log('ZMT extraction stderr:', stderr);
    }

    // List extracted files
    const extractedFiles = await fs.readdir(outputDir);
    return extractedFiles.map(f => path.join(outputDir, f));
  }

  /**
   * Extract media files (these are already in final format, so just copy)
   */
  private async extractMediaFiles(compressedPath: string, outputDir: string, method: CompressionMethod): Promise<string[]> {
    const fileName = path.basename(compressedPath);
    const outputPath = path.join(outputDir, fileName);

    await fs.copy(compressedPath, outputPath);
    return [outputPath];
  }

  /**
   * Extract TIFF image (copy the compressed file as it's already in final format)
   */
  private async extractTiffImage(compressedPath: string, outputDir: string): Promise<string[]> {
    const fileName = path.basename(compressedPath);
    const outputPath = path.join(outputDir, fileName);

    await fs.copy(compressedPath, outputPath);
    return [outputPath];
  }

  /**
   * Extract files using DICOM binary
   */
  private async extractWithDicom(compressedPath: string, outputDir: string): Promise<string[]> {
    const command = `cd "${outputDir}" && "${this.zmtDcmBinary}" x "${compressedPath}"`;

    console.log(`🏥 Running DICOM extraction: ${command}`);
    const { stdout, stderr } = await execAsync(command);

    if (stderr) {
      console.log('DICOM extraction stderr:', stderr);
    }

    // List extracted files
    const extractedFiles = await fs.readdir(outputDir);
    return extractedFiles.map(f => path.join(outputDir, f));
  }
}
